<template>
  <div class="h-full flex flex-col gap-4">
    <div class="p-6 flex items-center relative">
      <svg-icon name="go-back" class="absolute left-6" />
      <p class="title mx-auto">{{ $t("user.register.top") }}</p>
    </div>
    <div class="flex flex-col gap-4 px-4">
      <email ref="emailVerificationRef" />
      <van-form ref="formRef">
        <van-field
          v-model="formData.password"
          name="email"
          label="密码"
          placeholder="请输入密码"
          clearable
          label-align="top"
          type="password"
          :rules="[{ required: true, message: '请输入密码' }]"
        />
        <van-field
          v-model="formData.confirmPassword"
          center
          clearable
          label="确认密码"
          label-align="top"
          placeholder="请确认密码"
          type="password"
          :rules="[{ validator: confirmPasswordValidator }]"
        />
      </van-form>

      <button :loading="loading" class="submit-btn mt-4" @click="handleSubmit">
        {{ $t("user.register.confirm") }}
      </button>
      <p class="agree">{{ $t("user.login.agree") }}</p>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useData } from "@/hooks/useData";
import email from "./components/Email.vue";
import api from "@/api/index";

const { t } = useI18n();

const formData = reactive({
  username: "",
  verificationCode: "",
  password: "",
  confirmPassword: ""
});
const emailVerificationRef = ref(null);
const formRef = ref(null);

const confirmPasswordValidator = val => {
  if (val.length === 0) {
    return "请确认密码";
  }
  if (val === formData.password) {
    return true;
  }
  return "与新密码不一致";
};

const { error, data, loading, execute, onFetchResponse } = useData(
  api.login.register
);

const handleSubmit = async () => {
  const emailResult = emailVerificationRef.value.validate().valid;
  // 表单校验结果
  let formValid = false;
  try {
    await formRef.value.validate();
    formValid = true;
  } catch (errors) {
    console.error("表单校验失败：", errors);
  }
  if (!emailResult || !formValid) {
    return;
  }
  const req = {
    ...formData,
    ...emailVerificationRef.value.getFormData()
  };

  execute(req);
  console.log("提交数据:", req);
};
</script>
<style scoped>
.title {
  width: 80px;
  height: 28px;
  opacity: 1;

  font-family: Roboto;
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
  text-align: center;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #000000;

  z-index: 2;
}

.input-group {
  margin-bottom: 16px;
}

.input-group label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.input-container {
  position: relative;
  width: 100%;
}

.form-input {
  width: 100%;
  height: 48px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 0 16px;
  font-size: 16px;
  outline: none;
}

.form-input:focus {
  border-color: #0066ff;
}

.code-btn {
  margin-left: 10px;
  min-width: 110px;
  height: 48px;
  background-color: #7b68ee;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

.code-btn:disabled {
  background-color: #050303;
  cursor: not-allowed;
}

.submit-btn {
  width: 100%;
  height: 48px;
  background-color: #7b68ee;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

.description {
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #6b7280;
}

.agree {
  font-family: Roboto;
  font-size: 12px;
  font-weight: normal;
  line-height: 16px;
  text-align: center;
  letter-spacing: 0px;

  font-feature-settings: "kern" on;
  color: #9ca3af;
}

:deep(.van-cell) {
  padding: 10px 0;
}

:deep(.van-field__control) {
  width: 100%;
  height: 48px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 0 16px;
  font-size: 16px;
  outline: none;
}
</style>
